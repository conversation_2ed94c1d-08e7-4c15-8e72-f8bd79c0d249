import { CheckOutlined, RightOutlined } from '@ant-design/icons';
import { team_036_get_node_ctx_options } from "@common/api/http";
import AppNodeResourceIcon from "@components/AppNodeResourceIcon";
import { useQuerySetting327_getTeamSpaceAadmin, useQuerySetting407_getCodeValueList } from "@common/service/commonHooks";
import { isEmpty, treeToArray, getSysIconList, assembleGroup } from "@common/utils/ArrayUtils";
import { eCtxOptionType, eEnableFlg, eProductId } from "@common/utils/enum";
import { eCtxTypeId, eMenuStatus, getCtxIconByType, getMenuStatusIcon, eNameTextFontType } from "@common/utils/TsbConfig";
import { expiredModal, resourceMaxModal, unVipModal } from "@common/utils/ViewUtils";
import { Checkbox, Space, Skeleton, Modal } from "antd";
import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from "react";
import { Item as CtxMenuItem, Menu as CtxMenu, Submenu as CtxSubMenu, useContextMenu, Separator } from "react-contexify";
import { formatSvg } from "@common/utils/ViewUtils";
import "react-contexify/ReactContexify.css";
import { useMutation } from '@tanstack/react-query';
import "./ContextBoard.scss";
import * as https from "@common/api/http";
import { setting_320_get_node_priv_query } from "@common/api/query/query_setting";
import { globalUtil } from "@common/utils/globalUtil";


// 图标颜色
function ColorSelectedMenu({ selectValue, onChange, colorOptionsList, actionType }) {
  const _onChanged = (checkedValue) => {
    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : "" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值
    onChange && onChange({ id: actionType + "&" + value })
  }
  return <div style={{ display: "inline-block" }}>
    <Checkbox.Group value={[selectValue]} onChange={_onChanged}>
      {colorOptionsList.map(item => <Checkbox name="xxx" key={item.type.toString()} value={item.type.toString()} className={item.className} ></Checkbox>)}
    </Checkbox.Group>
  </div>
}

// 标题颜色
function TitleSelectedMenu({ selectValue, onChange, colorOptionsList, actionType }) {
  const _onChanged = (checkedValue) => {
    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : "" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值
    onChange && onChange({ id: actionType + "&" + value })
  }
  return <div style={{ display: "inline-block" }}>
    <Checkbox.Group value={[selectValue]} onChange={_onChanged} >
      {colorOptionsList.map(item => <Checkbox name="xxx" key={item.type.toString()} value={item.type.toString()} className={item.className} >
      { selectValue == item.type ? "" : item.title}
      </Checkbox>)}
    </Checkbox.Group>
  </div>
}

// 字体个性化设置，前段需要固定识别，存储的是type值
function FontSelectedMenu({ onChange, optionList, actionType }) {
  const _onChanged = (checkedValue) => {
    onChange && onChange({ id: actionType + "&" + checkedValue })
  }
  return <Space size={10}>
    {optionList.map((option)=>(
      <div key={option?.type} onClick={()=>_onChanged(option?.type)} className={option.className}>
       {formatSvg(option.icon)}
      </div>
    ))}
  </Space>
}

// 图标设置，无需固定，有多少显示多少，存储的是value值
function IconSelectedMenu({ onChange, optionList, actionType }) {
  const _onChanged = (checkedValue) => {
    onChange && onChange({ id: actionType + "&" + checkedValue })
  }
  return <Space size={10}>
    {optionList.map((option)=>(
      <div key={option?.type} onClick={()=>_onChanged(option?.value)} className={option.className}>
       {formatSvg(option.icon)}
      </div>
    ))}
  </Space>
}

/**
 * @description 文档图标
 */
/*function TitleIconSelectedMenu({ selectValue, onChange, colorOptionsList }) {
  const _onChanged = (checkedValue) => {
    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : "" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值
    onChange && onChange({ id: eCtxTypeId.ctx_42_set_icon + "&" + value })
  }
  return <div style={{ display: "inline-block" }}>
  <Checkbox.Group value={[selectValue]} onChange={_onChanged}>
    {colorOptionsList.map(option => (
      <Checkbox
        key={option.value?.toString()} 
        value={option.value?.toString()}
        className="checkbox-bage"
      >
        <div className="checkbox-bage-icon">
          {formatSvg(option.icon)}
        </div>
      </Checkbox>
    ))}
  </Checkbox.Group>
</div>
}*/

/*// checkbox 选中状态
const getCheckboxItem = (flag, label, color, className, actionType) => {
  return <div style={{ display: "flex", justifyContent: "space-between", alignItems: "baseline" }}>
    {/!* tmsbug-2622：删除线文案本身添加删除线 *!/}
    <span style={{ color: color }} className={`${actionType == eCtxTypeId.ctx_46_text_font ? "tree-dir-title-delete" : ""}`}>{label}</span>
    <Checkbox checked={flag} className={className} />
  </div>
}*/

// 收藏选中状态
const getCheckItem = (flag, label, color) => {
  return <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", color }}>
    <span>{label}</span>
    {flag ? <CheckOutlined style={{ color: "#70b603" }} /> : <></>}
  </div>
}

// 自定义延迟子菜单组件
const DelayedCtxSubMenu = ({ children, label, arrow, ...props }) => {
  const timeoutRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const menuRef = useRef(null);
  const subMenuRef = useRef(null);
  const isMouseInMenu = useRef(false);
  const [subMenuPosition, setSubMenuPosition] = useState({ top: 0 });
  const [subMenuHeight, setSubMenuHeight] = useState(0);

  // 计算预估的子菜单高度
  const calculateEstimatedHeight = () => {
    // 获取子菜单项的数量
    const menuItems = React.Children.toArray(children);
    const itemCount = menuItems.length;
    
    // 单个菜单项的高度（包括padding和border）
    const ITEM_HEIGHT = 28; // 根据实际样式调整
    // 子菜单的padding
    const MENU_PADDING = 8; // 上下padding各4px
    
    return itemCount * ITEM_HEIGHT + MENU_PADDING * 2;
  };

  // 添加 ResizeObserver 监听子菜单高度变化
  useEffect(() => {
    if (!subMenuRef.current) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        setSubMenuHeight(entry.contentRect.height);
      }
    });

    resizeObserver.observe(subMenuRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [isVisible]);

  const showSubMenu = () => {
    isMouseInMenu.current = true;
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // 计算子菜单位置，防止被屏幕底部遮挡
    if (menuRef.current) {
      const menuRect = menuRef.current.getBoundingClientRect();
      const estimatedHeight = calculateEstimatedHeight();
      const windowHeight = window.innerHeight;
      
      // 默认与父菜单项顶部对齐
      let topPosition = 0;
      
      // 检查子菜单底部是否会超出屏幕
      if (menuRect.top + estimatedHeight > windowHeight) {
        // 如果会超出屏幕底部，调整位置
        const overflow = menuRect.top + estimatedHeight - windowHeight;
        topPosition = Math.max(-menuRect.top, -overflow - 10); // 至少留10px的边距
      }
      
      setSubMenuPosition({ top: topPosition });
    }
    
    setIsVisible(true);
  };

  const hideSubMenu = () => {
    debugger
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      if (!isMouseInMenu.current) {
        setIsVisible(false);
      }
    }, 1000);
  };

  // 检查是否是有效的DOM节点，解决截图时焦点失去报错问题；
  const isValidNode = (node) => {
    try {
      return node && (node instanceof Node);
    } catch (e) {
      return false;
    }
  };

  const handleMouseEnter = () => {
    showSubMenu();
  };

  const handleMouseLeave = (e) => {
    
    // 检查鼠标是否移动到子菜单
    if (subMenuRef.current && isValidNode(e.relatedTarget) && subMenuRef.current.contains(e.relatedTarget)) {
      return;
    }
    
    // 检查鼠标是否移动到其他父菜单
    const parentMenu = menuRef.current?.parentElement;
    if (parentMenu && isValidNode(e.relatedTarget) && parentMenu.contains(e.relatedTarget)) {
      return;
    }

    isMouseInMenu.current = false;
    hideSubMenu();
  };

  const handleSubMenuMouseEnter = () => {
    showSubMenu();
  };

  const handleSubMenuMouseLeave = (e) => {
    
    // 检查鼠标是否移动到主菜单
    if (menuRef.current && isValidNode(e.relatedTarget) && menuRef.current.contains(e.relatedTarget)) {
      return;
    }

    // 检查鼠标是否移动到其他父菜单
    const parentMenu = menuRef.current?.parentElement;
    if (parentMenu && isValidNode(e.relatedTarget) && parentMenu.contains(e.relatedTarget)) {
      return;
    }

    isMouseInMenu.current = false;
    hideSubMenu();
  };

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <div 
      ref={menuRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ position: 'relative' }}
    >
      <CtxSubMenu 
        {...props}
        label={label}
        arrow={arrow}
        style={{
          ...props.style,
          position: 'absolute',
          left: '100%',
          top: subMenuPosition.top,
          zIndex: 999,
          display: isVisible ? 'block' : 'none'
        }}
      >
        <div 
          ref={subMenuRef}
          onMouseEnter={handleSubMenuMouseEnter}
          onMouseLeave={handleSubMenuMouseLeave}
          style={{ 
            width: '100%', 
            height: '100%',
            position: 'relative',
            zIndex: 1000
          }}
        >
          {children}
        </div>
      </CtxSubMenu>
    </div>
  );
};

/**
 * 右击菜单 
 * @param teamId 团队Id
 * @param onMoreBtnClick 非新建操作回调
 * @param onCreateBtnClick 新建等操作回调
 * @param id 菜单id
 * @param callbackParams 是 onMoreBtnClick 和 onCreateBtnClick 回调返回参数
 */
function ContextBoard({ teamId, onMoreBtnClick, onCreateBtnClick, id, handleOnVisibilityChange, ...callbackParams }, ref) {
  // const [createTypeList, setCreateTypeList] = useState([]); // tree right click context menu
  // const [nodeData,setNodeData] = useState(null); 
  const { data: { userId, teamAdminFlag: managerFlag } = {userId: undefined, teamAdminFlag: undefined} }
    = useQuerySetting327_getTeamSpaceAadmin({teamId, enabled: true}); // 判断登录人员是否是团队管理员
  const { data: selectionList } = useQuerySetting407_getCodeValueList(teamId); //字典数据

  const {isLoading:isCtxLoading, data:createTypeList=[], mutateAsync} = useMutation({
    mutationFn: ({nodeData,nodeId,filterActionTypes,ctxOptionList, nodeType}) => loadContextMenuList(nodeData,nodeId,filterActionTypes,ctxOptionList, nodeType)
  })

  const setting334Mutation = useMutation({
    mutationFn: https.setting_334_apply_authorization
  })

  //产品申请开通授权
  const applyProductAuthorize = (productId) => {
    if(!!productId) {
      setting334Mutation.mutate({ teamId, productId }, {
        onSuccess: (result) => {
          if(result.resultCode === 200) {
            //globalUtil.success("提交申请成功！");
            Modal.info({
              title: "提示",
              content: "提交成功，管理员会接收到申请信息，请您耐心等候",
              maskClosable: true,
              //centered: true, // 居中
              okText: "我知道了",
              width: 500,
            });
          }
        }
      })
    }
  }

  const cacheRef = useRef({});
  const { show } = useContextMenu({ id: id });

  useImperativeHandle(ref, () => ({
    /// const nodeData = {
    ///   nodeId,
    ///   rightFlgIconType： 图标颜色
    ///   nameTextColorType：名称颜色
    ///   nameTextStrikeFlg：删除线
    /// }
    showContextBoard: (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => _onShow(e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType)
  }));

  // load current node menu context and show
  // ctxOptionList：自定义传入ctxOptionList，无需接口获取
  const _onShow = async (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {
    // 计算菜单位置
    const menuHeight = 300; // 预估菜单高度
    const menuWidth = 200; // 预估菜单宽度
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;
    const clickY = e.clientY;
    const clickX = e.clientX;
    
    // 如果点击位置靠近底部，向上偏移
    const adjustedY = clickY + menuHeight > windowHeight ? 
      Math.max(0, windowHeight - menuHeight - 10) : 
      clickY;

    // 如果点击位置靠近右侧，向左偏移
    const adjustedX = clickX + menuWidth > windowWidth ?
      Math.max(0, windowWidth - menuWidth - 10) :
      clickX;

    // 使用 react-contexify 的默认定位机制，但添加位置调整
    show({ 
      event: e, 
      props: nodeData,
      position: {
        x: adjustedX,
        y: adjustedY
      }
    });
    
    mutateAsync({nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType});
    cacheRef.current.nodeData = nodeData;
  }

  // 特殊逻辑:1.由于报告模块同一份报告针对不同的人是不同类型的信件，根据nodeId无法区分出是发件箱,收件箱还是垃圾箱，所以需要前端传参nodeType来区分右击菜单
  // tmsbug-8830: 新建接口定义的菜单项位置调整，已经如果已匹配过，则不需要呈现
  const loadContextMenuList = async (nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {
    let result = !isEmpty(ctxOptionList) ? { resultCode: 200, ctxOptionList } : await team_036_get_node_ctx_options({ teamId, nodeId, nodeType})
    if (result.resultCode == 200) {
      let {ctxOptionList, favoriteFlg} = result;
      if(!isEmpty(filterActionTypes)) ctxOptionList = ctxOptionList.filter(ctxOption => !(filterActionTypes || []).some(actionType => ctxOption.actionType == actionType)) // 无需新建
      ctxOptionList.forEach(ctxOption => {
        switch (+ ctxOption.actionType) {
          case eCtxTypeId.ctx_38_create: // 新建操作
            ctxOption.children = assembleCreateTypeList(ctxOption.children);
            break;
          case eCtxTypeId.ctx_39_personalization: // 个性化设置
            ctxOption.children = assemblePersonalizationList(ctxOption.children, nodeData);
            break;
          case eCtxTypeId.ctx_60_flag_mail: // 标记邮件
            ctxOption.children = flagMailList(ctxOption.children, nodeData);
            break;
          case eCtxTypeId.ctx_37_favorite: // 收藏
            const favoriteFlag = favoriteFlg == eEnableFlg.enable;
            ctxOption.colorType = favoriteFlag ? eEnableFlg.disable : eEnableFlg.enable;
            ctxOption.name = getCheckItem(favoriteFlag, ctxOption.name);
            break;
          case eCtxTypeId.ctx_40_top: // 置顶（报告独有）
            const topFlag = nodeData.topFlg == eEnableFlg.enable;
            ctxOption.colorType = topFlag ? eEnableFlg.disable : eEnableFlg.enable;
            ctxOption.name = getCheckItem(topFlag, ctxOption.name);
            break;
          case eCtxTypeId.ctx_41_read_op: // 已读/未读（报告独有）
            const readFlag = nodeData.readFlg == eEnableFlg.enable;
            ctxOption.colorType = readFlag ? eEnableFlg.disable : eEnableFlg.enable;
            ctxOption.name = getCheckItem(readFlag, ctxOption.name);
            break;
          default:
            ctxOption.children = ctxOption.children.map((_sub)=>({..._sub, actionType: ctxOption.actionType, colorType: _sub.actionType}));
            break;
        }
      });
      return ctxOptionList;
    } else {
      return [];
    }
  }



  // 给新建菜单增加分组
  const assembleCreateTypeList = (createTypeList) => {
    let _createTypeList = assembleGroup(createTypeList, false);
    _createTypeList = treeToArray(_createTypeList);
    _createTypeList.forEach((_createType)=>{
      _createType.colorType = _createType.actionType;
      _createType.actionType = eCtxTypeId.ctx_38_create;
    });
    return _createTypeList;     
  }

  // 个性化设置
  const assemblePersonalizationList = (personalizationList, nodeData) => {
    let _personalizationList = assembleGroup(personalizationList, true);
    _personalizationList = _personalizationList.map( _personalization =>{
      if(_personalization.actionType == eCtxTypeId.ctx_18_set_figure_tag){ // 图标颜色
        const iconColorOptions = (_personalization.children || []).map(_child => {
          try {
            return JSON.parse(_child.name);
          } catch (error) {
            console.error("右击菜单个性化设置JSON格式不合法,请检查", _child.name );
            return {}
          }
        });
        _personalization.children = [
          {
            actionType: eCtxTypeId.ctx_18_set_figure_tag,
            name: <ColorSelectedMenu selectValue={nodeData.rightFlgIconType?.toString()} onChange={onClick} colorOptionsList = {iconColorOptions} actionType={eCtxTypeId.ctx_18_set_figure_tag}/>,
          }
        ] 
        return _personalization;
      } else if(_personalization.actionType == eCtxTypeId.ctx_3_color_txt){ // 标题颜色
        const textColorOptions = (_personalization.children || []).map(_child => {
          try {
            let option = JSON.parse(_child.name);
            return option;
          } catch (error) {
            console.error("右击菜单个性化设置JSON格式不合法,请检查", _child.name );
            return {}
          }
        });
       /*  _personalization.children = textColorOptions.map((option, index) => {
          let flag = option.type == nodeData.nameTextColorType;
          return {
            actionType: eCtxTypeId.ctx_3_color_txt,
            colorType: flag ? 0 : option.type,
            name: getCheckboxItem(flag, option.title, option.value, option.className, eCtxTypeId.ctx_3_color_txt),
          }
        }); */
        console.log("colorOptionsList", textColorOptions);
        _personalization.children = [
          {
            actionType: eCtxTypeId.ctx_3_color_txt,
            name: <TitleSelectedMenu selectValue={nodeData.nameTextColorType?.toString()} onChange={onClick} colorOptionsList = {textColorOptions} actionType={eCtxTypeId.ctx_3_color_txt}/>,
          }
        ] 
        return _personalization;
      } else if(_personalization.actionType == eCtxTypeId.ctx_46_text_font ){ // 设置字体个性化
        let textFontTypeOptions = (_personalization.children || []).map(_child => {
          try {
            let option =  JSON.parse(_child.name);
            let sysIconList = getSysIconList(selectionList); 
            option.icon = sysIconList.find(sys => sys.propType == option.value)?.propValue;
            const nameTextFontTypeList = nodeData.nameTextFontType?.split(",") || []; // 字体字段
            option.className = nameTextFontTypeList[eNameTextFontType[option.type].idx] == eEnableFlg.enable ? `checked-gray-box` : "";
            return option;
          } catch (error) {
            console.error("右击菜单个性化设置JSON格式不合法,请检查", _child.name );
            return {}
          }
        });
        console.log("textFontTypeOptions", textFontTypeOptions);
        _personalization.children = [
          {
            actionType: _personalization.actionType,
            name: <FontSelectedMenu selectValue={nodeData.nodeIconType?.toString()} onChange={onClick} optionList={textFontTypeOptions} actionType={_personalization.actionType}/>,
          }
        ] 
        return _personalization;
      } else if(_personalization.actionType == eCtxTypeId.ctx_42_set_icon){ // 设置图标
        let titleIconOptions = (_personalization.children || []).map(_child => {
          try {
            let option =  JSON.parse(_child.name);
            let sysIconList = getSysIconList(selectionList); 
            option.icon = sysIconList.find(sys => sys.propType == option.value)?.propValue;
            option.className = (nodeData.nodeIconType?.split(",") || []).some(nodeIcon => option.value == nodeIcon ) ? `checked-gray-box` : "";
            return option;
          } catch (error) {
            console.error("右击菜单个性化设置JSON格式不合法,请检查", _child.name );
            return {}
          }
        });
        _personalization.children = [
          {
            actionType: _personalization.actionType,
            name: <IconSelectedMenu selectValue={nodeData.nodeIconType?.toString()} onChange={onClick} optionList={titleIconOptions} actionType={_personalization.actionType}/>,
          }
        ] 
        return _personalization;
      } else  {
        return _personalization;
      }
    })
    _personalizationList = treeToArray(_personalizationList);
    return _personalizationList;
  }

  // 标记邮件
  const flagMailList = (personalizationList, nodeData) => {
    let _personalizationList = assembleGroup(personalizationList, true);
    _personalizationList = _personalizationList.map( _personalization =>{
      if(_personalization.actionType == eCtxTypeId.ctx_61_flag_color){ // 标记颜色
        const iconColorOptions = (_personalization.children || []).map(_child => {
          try {
            return JSON.parse(_child.name);
          } catch (error) {
            console.error("右击菜单个性化设置JSON格式不合法,请检查", _child.name );
            return {}
          }
        });
        _personalization.children = [
          {
            actionType: _personalization.actionType,
            name: <ColorSelectedMenu selectValue={nodeData.tagColor?.toString()} onChange={onClick} colorOptionsList = {iconColorOptions} actionType={_personalization.actionType}/>,
          }
        ] 
        return _personalization;
      }  else if(_personalization.actionType == eCtxTypeId.ctx_62_flag_img){ // 标记图标
        let titleIconOptions = (_personalization.children || []).map(_child => {
          try {
            let option =  JSON.parse(_child.name);
            option.icon = getSysIconList(selectionList).find(sys => sys.propType == option.value)?.propValue;
            option.className = (nodeData.tagColor?.split(",") || []).some(nodeIcon => option.value == nodeIcon ) ? `checked-gray-box` : "";
            return option;
          } catch (error) {
            console.error("右击菜单个性化设置JSON格式不合法,请检查", _child.name );
            return {}
          }
        });
        _personalization.children = [
          {
            actionType: _personalization.actionType,
            name: <IconSelectedMenu selectValue={nodeData.tagColor?.toString()} onChange={onClick} optionList={titleIconOptions} actionType={_personalization.actionType}/>,
          }
        ] 
        return _personalization;
      } else  {
        return _personalization;
      }
    })
    _personalizationList =  _personalizationList.flatMap((taskGroup) => taskGroup.children);
    return _personalizationList;
  }


  // menu item click
  const onClick = ({ id, props, data, triggerEvent, ...args }) => {
    let arr = id.split("&") // 存在快捷方式-999的nodeType
    let callbackData = {
      actionType: arr[0],
      colorType: arr[1],
      objType: data?.objType,
      productId: data?.productId //20250724 Jim Song 后端多加一个参数productId,用来表征如果menuStatus异常时，知晓是哪一个产品id
    }
    console.log(callbackData)
    onContextMenuClick(callbackData)
  }

  const _onMoreBtnClick = (e) => {
    onMoreBtnClick && onMoreBtnClick({ nodeItem: cacheRef.current.nodeData, ctxType: e.actionType, colorType: e.colorType, ...callbackParams })
  }

  const _onCreateBtnClick = (e) => {
    onCreateBtnClick && onCreateBtnClick({ nodeItem: cacheRef.current.nodeData, nodeType: e.colorType, ...callbackParams})
  }

  const onContextMenuClick = ({ actionType, colorType, productId }) => {
    if (actionType == eCtxTypeId.ctx_38_create) { // 新建操作
      // 注意:设置图标颜色无法查询出来
      const node = findByActionAndColorType(createTypeList, actionType, colorType);
      if(node.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ){ // 免费对象数已达上限
        return resourceMaxModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);
      }
      if(node.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ){ // Vip未授权
        return unVipModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId,applyProductAuthorize);
      }
      if(node.menuStatus == eMenuStatus.status_4_Vip_Expired ){ // Vip已过期
        return expiredModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);
      }
      _onCreateBtnClick({ colorType })
    } else {
      _onMoreBtnClick({ actionType, colorType })
    }
  }

  // 根据actionType和colorType查找节点
  function findByActionAndColorType(list, actionType, colorType) {
      for (let i in list) {
        if (list[i].actionType == actionType && list[i].colorType == colorType) {
          return list[i];
        }
        if (list[i].children) {
          let node = findByActionAndColorType(list[i].children, actionType, colorType);
          if (node) {
            return node
          }
        }
      }
  }

  // 右击菜单
  // CtxMenuItem、CtxSubmenu <'fade' | 'scale' | 'flip' | 'slide'>
  return (
    <CtxMenu id={id} animation={{ enter: false, exit: 'slide' }} onVisibilityChange={handleOnVisibilityChange} className="">
      {isCtxLoading && <ContextLoadingBoard />}
      {/* 无可用选项 */}
      {!isCtxLoading && createTypeList.length == 0 && <CtxMenuItem disabled> 无可用选项 </CtxMenuItem>}
      {/* 逻辑确定只有两层所以这么处理 */}
      {!isCtxLoading && createTypeList.length > 0 && createTypeList.map(el => {
        const key = `${el.actionType}&${el.colorType}`;
        if (!isEmpty(el.children)) {
          return <DelayedCtxSubMenu key={key} label={
            <>
              <span className={"iconfont " + getCtxIconByType(el.actionType) + " fontsize-12 fontcolor-normal marginRight-5"}></span>
              <span>{el.name}</span>
            </>}
            // 子级菜单箭头
            arrow={<RightOutlined />}
          >
            {/* 横向子菜单面板 - 只对新建菜单使用 */}
            <div className={el.actionType == eCtxTypeId.ctx_38_create ? "horizontal-submenu-panel" : ""}>
              {el.actionType == eCtxTypeId.ctx_38_create ? (
                // 新建菜单：按分组显示
                (() => {
                  const items = [];
                  let currentGroupItems = [];
                  
                  el.children.forEach((sub, index) => {
                    const key = `${sub.actionType}&${sub.colorType}`;
                    
                    // 如果是分组标题
                    if (sub.type == eCtxOptionType.eGroup) {
                      // 如果有累积的分组内容，先渲染它们
                      if (currentGroupItems.length > 0) {
                        items.push(
                          <div key={`group-${index}-content`} className="group-content-row">
                            {currentGroupItems}
                          </div>
                        );
                        currentGroupItems = [];
                      }
                      
                      // 添加分组标题
                      items.push(
                        <div key={index} className="group-title-row">
                          <div className="group-title">{sub.name}</div>
                        </div>
                      );
                    } else {
                      // 如果是分组内容
                      currentGroupItems.push(
                        <div key={index} className="submenu-item-wrapper normal-menu-item">
                          <CtxMenuItem
                            id={key}
                            data={sub}
                            disabled={sub.disabled}
                            onClick={onClick}
                          >
                            {
                              sub.actionType == eCtxTypeId.ctx_38_create ?
                              <AppNodeResourceIcon nodeType={sub.colorType} className="fontsize-12 fontcolor-normal marginRight-5" style={{lineHeight: "24px"}}/>: 
                              sub.actionType == eCtxTypeId.ctx_12_create_shortcut ?
                              (getCtxIconByType(sub.colorType) && <span className={"iconfont " + getCtxIconByType(sub.colorType) + " fontsize-12 fontcolor-normal marginRight-5"}></span>) :
                              <span></span>
                            }
                            <div style={{ width: "100%" }}>{sub.name}</div>
                            {/* 对象状态图标 */}
                            {
                              <span className = {`iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `}
                               style={{color: getMenuStatusIcon(sub.menuStatus).iconColor}}
                               title={sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ?
                                   '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ?
                                                    '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ?
                                                                   '应用已过期' : ''}>
                              </span>
                            }
                            <span></span>
                          </CtxMenuItem>
                        </div>
                      );
                    }
                  });
                  
                  // 处理最后的分组内容
                  if (currentGroupItems.length > 0) {
                    items.push(
                      <div key="group-last-content" className="group-content-row">
                        {currentGroupItems}
                      </div>
                    );
                  }
                  
                  return items;
                })()
              ) : (
                // 其他菜单：保持原有布局
                el.children.map((sub, index) => {
                  const key = `${sub.actionType}&${sub.colorType}`;
                  return (
                    <div key={index} className="">
                      <CtxMenuItem
                        id={key}
                        data={sub}
                        disabled={sub.disabled}
                        onClick={(
                          sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || 
                          sub.actionType == eCtxTypeId.ctx_3_color_txt || 
                          sub.actionType == eCtxTypeId.ctx_42_set_icon || 
                          sub.actionType == eCtxTypeId.ctx_46_text_font ||
                          sub.actionType == eCtxTypeId.ctx_61_flag_color ||
                          sub.actionType == eCtxTypeId.ctx_62_flag_img
                          ) ? () => {} : onClick
                        }
                        style={sub.type != eCtxOptionType.eGroup ? { paddingLeft: "24px" } : {}}
                        className={ (sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || 
                          sub.actionType == eCtxTypeId.ctx_3_color_txt || 
                          sub.actionType == eCtxTypeId.ctx_42_set_icon || 
                          sub.actionType == eCtxTypeId.ctx_46_text_font ||
                          sub.actionType == eCtxTypeId.ctx_61_flag_color ||
                          sub.actionType == eCtxTypeId.ctx_62_flag_img
                          ) ? "context-item-not-focus" : "" }
                      >
                        {
                          sub.actionType == eCtxTypeId.ctx_38_create ?
                          <AppNodeResourceIcon nodeType={sub.colorType} className="fontsize-12 fontcolor-normal marginRight-5" style={{lineHeight: "24px"}}/>: 
                          sub.actionType == eCtxTypeId.ctx_12_create_shortcut ?
                          (getCtxIconByType(sub.colorType) && <span className={"iconfont " + getCtxIconByType(sub.colorType) + " fontsize-12 fontcolor-normal marginRight-5"}></span>) :
                          <span></span>
                        }
                        <div style={{ width: "100%" }}>{sub.name}</div>
                        {/* 对象状态图标 */}
                        {
                          <span className = {`iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `}
                           style={{color: getMenuStatusIcon(sub.menuStatus).iconColor}}
                           title={sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ?
                               '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ?
                                                '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ?
                                                               '应用已过期' : ''}>
                          </span>
                        }
                        <span></span>
                      </CtxMenuItem>
                    </div>
                  );
                })
              )}
            </div>
          </DelayedCtxSubMenu>
        } else {
          return <CtxMenuItem key={key} id={key} onClick={onClick} >
            {el.actionType == 28 ?
            <span className={"iconfont " + getCtxIconByType(el.actionType) + ' fontsize-12 fontcolor-normal'} style={{marginLeft:-5,marginRight:5}}></span>
            :
            <span className={"iconfont " + getCtxIconByType(el.actionType) + " fontsize-12 fontcolor-normal marginRight-5"}></span>
            }
            <div style={{ width: "100%" }}>{el.name}</div>
          </CtxMenuItem>
        }
      })}

    </CtxMenu>
  )
}


function ContextLoadingBoard() {
  return <React.Fragment>
    <CtxMenuItem disabled>
      <Skeleton.Input active={true} style={{height:"24px",overflow:"hidden"}}/>
    </CtxMenuItem>
    <CtxMenuItem disabled>
      <Skeleton.Input active={true} style={{height:"24px",overflow:"hidden"}}/>
    </CtxMenuItem>
    <CtxMenuItem disabled>
      <Skeleton.Input active={true} style={{height:"24px",overflow:"hidden"}}/>
    </CtxMenuItem>
    <CtxMenuItem disabled>
      <Skeleton.Input active={true} style={{height:"24px",overflow:"hidden"}}/>
    </CtxMenuItem>
    <CtxMenuItem disabled>
      <Skeleton.Input active={true} style={{height:"24px",overflow:"hidden"}}/>
    </CtxMenuItem>
  </React.Fragment>
}


export default forwardRef(ContextBoard)

