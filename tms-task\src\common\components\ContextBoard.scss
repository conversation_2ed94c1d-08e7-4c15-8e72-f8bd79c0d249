@import "src/common/styles/utils/variables.scss";

.contexify{
  min-width: 188px;
  font-size: 12px!important;
  font-weight: normal;  // 避免受PageHeader的ant-page-header-heading-title的font-weight影响
  line-height: 28px;    // 避免受PageHeader的ant-page-header-heading-title的line-height影响
  --contexify-zIndex: 10001;
}

.contexify_itemContent {
  padding: 0px 6px!important;
}


// .contexify {}

// .contexify__submenu--is-open,
// .contexify__submenu--is-open > .contexify__item__content {}

// .contexify__submenu--is-open > .contexify__submenu {}

// .contexify .contexify__submenu {}

// .contexify__submenu-arrow {}

// .contexify__separator {}

// .contexify__will-leave--disabled {}

// .contexify__item {}

// .contexify__item:not(.contexify__item--disabled):focus {}

// .contexify__item:not(.contexify__item--disabled):hover > .contexify__item__content,
// .contexify__item:not(.contexify__item--disabled):focus > .contexify__item__content {}

// .contexify__item:not(.contexify__item--disabled):hover > .contexify__submenu {}

// .contexify__item--disabled {}

// .contexify__item__content {}

.contexify_item:not(.contexify_item-disabled)[aria-haspopup]:hover>.contexify_itemContent .contexify_rightSlot,
.contexify_item:not(.contexify_item-disabled)[aria-haspopup]:focus>.contexify_itemContent .contexify_rightSlot {
  color: var(--contexify-arrow-color);
}

// 选择颜色
.contexify_item:not(.contexify_item-disabled):focus>.contexify_itemContent, .contexify_item:not(.contexify_item-disabled):hover>.contexify_itemContent {
  color: #666;
  background-color: #f5f5f5;
}

// tmsbug-6228:个性化功能，UI优化
// 图标颜色、标题颜色、有单独悬浮背景色，无需整体选中背景色
.context-item-not-focus:not(.contexify_item-disabled):focus>.contexify_itemContent, .context-item-not-focus:not(.contexify_item-disabled):hover>.contexify_itemContent {
  color: #666;
  background-color: #fff;
}

@each $type, $id, $color in $contextcolor-list {
  // 图标颜色
  .tree-more-select-color-#{$id} {
    .ant-checkbox-inner{
      width: 20px;
      height: 20px;
      line-height: 20px;
      border-radius: 50%;
      background-color: $color;
      border-color: $color;
      &:hover{
        border: none;
    }
  }
    .ant-checkbox-checked:after{
      border: 1px solid $color;
  }
}
  // 标题颜色
  .title-checked-color-#{$id} {
    .ant-checkbox-inner{
      position: relative;
      width: 20px;
      height: 20px;
      line-height: 20px;
      border-radius: 50%;
      border-color: $color;
      &:hover{
        border: none;
    }
  }
    .ant-checkbox-checked:after{
      border: 1px solid $color;
  }
    .ant-checkbox-checked .ant-checkbox-inner{ // 选中时显示背景色
        background-color: $color;
  }
}
  .title-checked-color-#{$id}.ant-checkbox-wrapper {
    position: relative;
}
  .title-checked-color-#{$id}.ant-checkbox-wrapper span:nth-of-type(2){ // 标题
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px; // chrome浏览器限制的字体最小值
    color: $color;
}
}

@keyframes contexify_scaleIn {
from {
opacity: 0;
transform: scale3d(0.3, 0.3, 0.3);
}
to {
opacity: 1;
}
}
@keyframes contexify_scaleOut {
from {
opacity: 1;
}
to {
opacity: 0;
transform: scale3d(0.3, 0.3, 0.3);
}
}

.contexify_willEnter-tms {
  transform-origin: top left;
  animation: contexify_scaleIn 3s linear 10s;
}

.contexify_willLeave-tms {
  transform-origin: top left;
  animation: contexify_scaleOut 3s linear 10s;
}



// 定制check大小
.checkbox-bage {
  position: relative;
  // z-index: 10;
  .ant-checkbox-checked,
  .ant-checkbox {
    .ant-checkbox-inner {
      width: 10px;
      height: 10px;
  }
    // 选中√
    .ant-checkbox-inner::after {
      width: 3px;
      height: 6px;
  }
}
  // 图片
  .checkbox-bage-icon{
    position: absolute;
    left: -8px;
    bottom: -6px;
}
}

// 选中灰色背景
.checked-gray-box {
  background-color: #F2F5FD;
  border-radius: 5px;
  padding: 4px 8px;
}

// 横向子菜单面板样式 - 只用于新建菜单
.horizontal-submenu-panel {
  display: flex;
  flex-direction: column;
  gap: 0px;
  padding: 6px;
  min-width: 400px;
  max-width: 600px;
  
  // 分组标题行
  .group-title-row {
    width: 100%;
    margin-bottom: 0px;
    
    .group-title {
      font-size: 14px;
      font-weight: bold;
      padding: 1px 8px;
    }
  }
  
  // 分组内容行
  .group-content-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 0px;
    row-gap: 0px; /* 同一组内换行的行间距设为0 */
    
    .submenu-item-wrapper {
      flex: 0 0 calc(33.333% - 7px);
      min-width: 120px;
      max-width: 150px;
      
      .contexify_itemContent {
        padding: 4px 6px !important;
        display: flex;
        align-items: center;
        border-radius: 4px;
        transition: background-color 0.2s ease;
        
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
  
  // 普通菜单项（非分组内容）
  .submenu-item-wrapper {
    flex: 0 0 calc(33.333% - 7px);
    min-width: 120px;
    max-width: 150px;
    
    .contexify_itemContent {
      padding: 4px 6px !important;
      display: flex;
      align-items: center;
      border-radius: 4px;
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

// 覆盖原有的contexify子菜单样式 - 只对新建菜单
.contexify__submenu {
  .horizontal-submenu-panel {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid #e8e8e8;
  }
}